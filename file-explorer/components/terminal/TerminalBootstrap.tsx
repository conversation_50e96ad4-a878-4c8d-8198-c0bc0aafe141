import { useEffect, useRef, useState } from 'react';
import { Terminal } from '@xterm/xterm';
import { FitAddon } from '@xterm/addon-fit';
import { WebLinksAddon } from '@xterm/addon-web-links';
import '@xterm/xterm/css/xterm.css';

interface TerminalBootstrapProps {
  className?: string;
  onReady?: (terminal: Terminal) => void;
}

export default function TerminalBootstrap({ 
  className = '', 
  onReady 
}: TerminalBootstrapProps) {
  const terminalRef = useRef<HTMLDivElement>(null);
  const terminalInstance = useRef<Terminal | null>(null);
  const fitAddon = useRef<FitAddon | null>(null);
  const webLinksAddon = useRef<WebLinksAddon | null>(null);
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    if (!terminalRef.current || terminalInstance.current) {
      return;
    }

    try {
      // Initialize terminal with basic configuration
      const terminal = new Terminal({
        cursorBlink: true,
        fontSize: 14,
        fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
        theme: {
          background: '#1a1a1a',
          foreground: '#ffffff',
          cursor: '#ffffff',
          selectionBackground: '#3a3a3a',
        },
        cols: 80,
        rows: 24,
      });

      // Initialize addons
      const fit = new FitAddon();
      const webLinks = new WebLinksAddon();

      // Load addons
      terminal.loadAddon(fit);
      terminal.loadAddon(webLinks);

      // Store references
      terminalInstance.current = terminal;
      fitAddon.current = fit;
      webLinksAddon.current = webLinks;

      // Open terminal in DOM
      terminal.open(terminalRef.current);

      // Fit terminal to container
      fit.fit();

      // Focus terminal
      terminal.focus();

      // Welcome message
      terminal.writeln('🟢 Terminal Bootstrap Initialized - Ready for Input');
      terminal.writeln('📝 Type anything to test echo functionality...');
      terminal.write('\r\n$ ');

      // Simple echo functionality for testing
      terminal.onData((data) => {
        // Handle basic input
        if (data === '\r') {
          // Enter key
          terminal.write('\r\n$ ');
        } else if (data === '\u007F') {
          // Backspace
          terminal.write('\b \b');
        } else {
          // Regular character
          terminal.write(data);
        }
      });

      // Handle resize events
      const handleResize = () => {
        if (fit) {
          fit.fit();
        }
      };

      window.addEventListener('resize', handleResize);

      // Mark as ready
      setIsReady(true);
      onReady?.(terminal);

      console.log('✅ TerminalBootstrap: Initialized successfully');

      // Cleanup function
      return () => {
        window.removeEventListener('resize', handleResize);
        if (terminalInstance.current) {
          terminalInstance.current.dispose();
          terminalInstance.current = null;
        }
        fitAddon.current = null;
        webLinksAddon.current = null;
        setIsReady(false);
      };
    } catch (error) {
      console.error('❌ TerminalBootstrap: Initialization failed:', error);
    }
  }, [onReady]);

  return (
    <div className={`terminal-bootstrap-container ${className}`}>
      <div
        ref={terminalRef}
        id="terminal-container"
        className="w-full h-full min-h-[400px] bg-[#1a1a1a] rounded-md"
        style={{
          width: '100%',
          height: '100%',
        }}
      />
      {!isReady && (
        <div className="absolute inset-0 flex items-center justify-center bg-[#1a1a1a] text-white">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
            <p>Initializing Terminal...</p>
          </div>
        </div>
      )}
    </div>
  );
}
