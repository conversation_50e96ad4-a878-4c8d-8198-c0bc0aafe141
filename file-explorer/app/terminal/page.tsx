"use client"

import { TerminalBootstrap } from '@/components/terminal'
import { Terminal } from '@xterm/xterm'
import { useState } from 'react'

export default function TerminalTestPage() {
  const [terminalInstance, setTerminalInstance] = useState<Terminal | null>(null)

  const handleTerminalReady = (terminal: Terminal) => {
    setTerminalInstance(terminal)
    console.log('✅ Terminal ready in test page:', terminal)
  }

  return (
    <div className="h-screen flex flex-col bg-background">
      <div className="h-12 border-b border-border flex items-center px-4 bg-background">
        <h1 className="text-lg font-semibold">Terminal Bootstrap Test</h1>
        <div className="ml-auto text-sm text-muted-foreground">
          Status: {terminalInstance ? '🟢 Ready' : '🟡 Initializing...'}
        </div>
      </div>
      
      <div className="flex-1 p-4">
        <div className="h-full border border-border rounded-lg overflow-hidden">
          <TerminalBootstrap 
            className="h-full"
            onReady={handleTerminalReady}
          />
        </div>
      </div>
      
      <div className="h-8 border-t border-border bg-muted/50 flex items-center px-4 text-xs text-muted-foreground">
        Terminal Bootstrap Test - Phase 1 Verification
      </div>
    </div>
  )
}
