export interface ElectronAPI {
  selectFolder: () => Promise<{
    success: boolean;
    path?: string;
    name?: string;
  }>;
  readDirectory: (dirPath: string) => Promise<{
    success: boolean;
    items?: any[];
    error?: string;
  }>;
  readFile: (filePath: string) => Promise<{
    success: boolean;
    content?: string;
    error?: string;
  }>;
  saveFile: (filePath: string, content: string) => Promise<{
    success: boolean;
    error?: string;
  }>;
  createFile: (filePath: string, content?: string) => Promise<{
    success: boolean;
    error?: string;
  }>;
  deleteFile: (filePath: string) => Promise<{
    success: boolean;
    error?: string;
  }>;
  openTerminalWindow: () => void;
  openKanbanWindow: (boardId: string) => void;
  openAgentSystemWindow: () => void;
  openEditorWindow: (filePath?: string) => void;
  openExplorerWindow: () => void;
  openChatWindow: () => void;
  openTimelineWindow: () => void;
  closeWindow: () => void;
  minimizeWindow: () => void;
  executeCommand: (command: string, cwd?: string) => Promise<{
    success: boolean;
    output?: string;
    error?: string;
    exitCode?: number;
  }>;
  getCurrentWorkingDirectory: () => Promise<string>;

  // Notification API
  notification?: {
    show: (options: {
      title: string;
      body: string;
      icon?: string;
      silent?: boolean;
    }) => void;
  };

  // LLM API for AI provider integration
  llm?: {
    validateApiKey: (provider: string, apiKey: string) => Promise<boolean>;
    callLLM: (provider: string, request: any, apiKey: string) => Promise<{
      content: string;
      tokensUsed: {
        input: number;
        output: number;
        total: number;
      };
      model: string;
      finishReason: string;
    }>;
    fetchModels: (provider: string, apiKey: string) => Promise<string[]>;
  };

  // ✅ MCP Protocol API
  mcp?: {
    initializeConnection: (serverId: string, config: {
      command: string;
      args: string[];
      timeout: number;
      maxRetries: number;
    }) => Promise<{
      success: boolean;
      error?: string;
      serverInfo?: {
        name: string;
        version: string;
        capabilities: string[];
      };
    }>;
    sendTask: (serverId: string, request: {
      task: string;
      context: any;
      agentId: string;
      messages: Array<{
        role: string;
        content: string;
      }>;
      metadata?: {
        maxTokens?: number;
        temperature?: number;
        systemPrompt?: string;
      };
    }) => Promise<{
      success: boolean;
      content?: string;
      tokensUsed?: {
        prompt: number;
        completion: number;
        total: number;
      };
      model?: string;
      finishReason?: string;
      error?: string;
      toolsInvoked?: string[];
    }>;
    syncAgentState: (serverId: string, agentId: string, state: {
      currentTask?: string;
      status: 'idle' | 'working' | 'error';
      lastUpdate: number;
      metadata?: Record<string, any>;
    }) => Promise<{
      success: boolean;
      error?: string;
    }>;
    testConnection: (serverId: string) => Promise<{
      success: boolean;
      error?: string;
    }>;
    disconnectServer: (serverId: string) => Promise<{
      success: boolean;
      error?: string;
    }>;
    getConnectedServers: () => Promise<string[]>;
  };



  // Generic IPC methods for real-time synchronization
  ipc?: {
    send: (channel: string, ...args: any[]) => void;
    invoke: (channel: string, ...args: any[]) => Promise<any>;
    on: (channel: string, listener: (...args: any[]) => void) => () => void;
    removeAllListeners: (channel: string) => void;
  };

  // Configuration Store API
  configStore?: {
    initialize: () => Promise<void>;
    createProject: (config: any) => Promise<any>;
    getProject: (id: string) => Promise<any>;
    getProjectByPath: (path: string) => Promise<any>;
    getAllProjects: () => Promise<any[]>;
    updateProject: (id: string, updates: any) => Promise<any>;
    deleteProject: (id: string) => Promise<boolean>;
    setGlobalSetting: (category: string, key: string, value: any, encrypted?: boolean) => Promise<void>;
    getGlobalSetting: (category: string, key: string) => Promise<any>;
    getGlobalSettingsByCategory: (category: string) => Promise<Record<string, any>>;
    deleteGlobalSetting: (category: string, key: string) => Promise<boolean>;
    backupDatabase: (backupPath: string) => Promise<void>;
    vacuumDatabase: () => Promise<void>;
  };
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}